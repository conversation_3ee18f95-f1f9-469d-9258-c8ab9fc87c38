import { QueryClient } from "@tanstack/react-query";

// Create a query client with optimized defaults
export const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			// Stale time: how long data is considered fresh (5 minutes)
			staleTime: 5 * 60 * 1000,
			// Cache time: how long data stays in cache when unused (10 minutes)
			gcTime: 10 * 60 * 1000,
			// Retry failed requests 3 times with exponential backoff
			retry: (failureCount, error: any) => {
				// Don't retry on 4xx errors (client errors)
				if (error?.status >= 400 && error?.status < 500) {
					return false;
				}
				// Retry up to 3 times for other errors
				return failureCount < 3;
			},
			// Retry delay with exponential backoff
			retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
			// Refetch on window focus for important data
			refetchOnWindowFocus: false,
			// Refetch on reconnect
			refetchOnReconnect: true,
		},
		mutations: {
			// Retry mutations once on network errors
			retry: (failureCount, error: any) => {
				// Don't retry on 4xx errors
				if (error?.status >= 400 && error?.status < 500) {
					return false;
				}
				// Retry once for network errors
				return failureCount < 1;
			},
		},
	},
});

// Query keys factory for consistent key management
export const queryKeys = {
	// Authentication related queries
	auth: {
		all: ["auth"] as const,
		session: () => [...queryKeys.auth.all, "session"] as const,
		user: () => [...queryKeys.auth.all, "user"] as const,
	},
	// Districts related queries
	districts: {
		all: ["districts"] as const,
		list: (params?: { pageNumber?: number; pageSize?: number }) =>
			[...queryKeys.districts.all, "list", params] as const,
	},
	// Add more query keys as needed
} as const;
