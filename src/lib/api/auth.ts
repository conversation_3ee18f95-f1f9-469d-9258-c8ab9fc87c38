import { apiClient } from "./client";
import type {
	LoginRequest,
	LoginResponse,
	RegisterRequest,
	RegisterResponse,
	ForgotPasswordRequest,
	ForgotPasswordResponse,
	ResetPasswordRequest,
	ResetPasswordResponse,
} from "./types";

export const authApi = {
	// Login user
	login: async (credentials: LoginRequest): Promise<LoginResponse> => {
		return apiClient.post<LoginResponse>("/api/auth/login", credentials);
	},

	// Register new user
	register: async (userData: RegisterRequest): Promise<RegisterResponse> => {
		return apiClient.post<RegisterResponse>("/api/auth/register", userData);
	},

	// Request password reset
	forgotPassword: async (
		data: ForgotPasswordRequest,
	): Promise<ForgotPasswordResponse> => {
		return apiClient.post<ForgotPasswordResponse>(
			"/api/auth/forgot-password",
			data,
		);
	},

	// Reset password with token
	resetPassword: async (
		data: ResetPasswordRequest,
	): Promise<ResetPasswordResponse> => {
		return apiClient.post<ResetPasswordResponse>(
			"/api/auth/reset-password",
			data,
		);
	},

	// Verify email/account activation
	verifyEmail: async (token: string): Promise<{ message: string }> => {
		return apiClient.post<{ message: string }>("/api/auth/verify-email", {
			token,
		});
	},

	// Refresh token (if endpoint exists)
	refreshToken: async (): Promise<{ token: string }> => {
		return apiClient.post<{ token: string }>("/api/auth/refresh");
	},

	// Logout (if endpoint exists for server-side logout)
	logout: async (): Promise<{ message: string }> => {
		return apiClient.post<{ message: string }>("/api/auth/logout");
	},
};
