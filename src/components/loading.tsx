import { cn } from "@/lib/utils";

interface LoadingProps {
	size?: "sm" | "md" | "lg";
	className?: string;
	text?: string;
}

export function Loading({ size = "md", className, text }: LoadingProps) {
	const sizeClasses = {
		sm: "h-4 w-4",
		md: "h-6 w-6",
		lg: "h-8 w-8",
	};

	return (
		<div className={cn("flex items-center justify-center gap-2", className)}>
			<div
				className={cn(
					"animate-spin rounded-full border-2 border-muted border-t-primary",
					sizeClasses[size],
				)}
			/>
			{text && <span className="text-sm text-muted-foreground">{text}</span>}
		</div>
	);
}

interface PageLoadingProps {
	text?: string;
}

export function PageLoading({ text = "Loading..." }: PageLoadingProps) {
	return (
		<div className="min-h-screen flex items-center justify-center">
			<Loading size="lg" text={text} />
		</div>
	);
}

interface InlineLoadingProps {
	text?: string;
	className?: string;
}

export function InlineLoading({ text, className }: InlineLoadingProps) {
	return <Loading size="sm" text={text} className={className} />;
}
