import { useQuery } from "@tanstack/react-query";
import { districtsApi } from "@/lib/api";
import { queryKeys } from "@/lib/query-client";
import type { DistrictsRequest } from "@/lib/api/types";

// Hook to fetch districts with pagination
export function useDistricts(params: DistrictsRequest = { pageNumber: 1, pageSize: 300 }) {
	return useQuery({
		queryKey: queryKeys.districts.list(params),
		queryFn: () => districtsApi.getAll(params),
		staleTime: 10 * 60 * 1000, // 10 minutes - districts don't change often
		gcTime: 30 * 60 * 1000, // 30 minutes
		retry: 3,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
}

// Hook to fetch all districts (convenience hook with default pagination)
export function useAllDistricts() {
	return useDistricts({ pageNumber: 1, pageSize: 300 });
}

// Hook to get districts as options for select/combobox components
export function useDistrictOptions() {
	const { data, isLoading, error } = useAllDistricts();

	const options = data?.data?.map((district) => ({
		value: district.id,
		label: district.name,
		region: district.region,
	})) || [];

	return {
		options,
		isLoading,
		error,
	};
}
