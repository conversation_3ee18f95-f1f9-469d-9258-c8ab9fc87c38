import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

export interface User {
	userId: string;
	userName: string;
	tenantId: string;
	modules: string[];
}

export interface AuthState {
	// State
	user: User | null;
	token: string | null;
	isAuthenticated: boolean;
	isLoading: boolean;

	// Actions
	setUser: (user: User) => void;
	setToken: (token: string) => void;
	setLoading: (loading: boolean) => void;
	login: (token: string, user: User) => void;
	logout: () => void;
	initialize: () => void;
}

export const useAuthStore = create<AuthState>()(
	persist(
		(set, get) => ({
			// Initial state
			user: null,
			token: null,
			isAuthenticated: false,
			isLoading: true,

			// Actions
			setUser: (user: User) => {
				set({ user, isAuthenticated: !!user });
			},

			setToken: (token: string) => {
				set({ token });
			},

			setLoading: (isLoading: boolean) => {
				set({ isLoading });
			},

			login: (token: string, user: User) => {
				set({
					token,
					user,
					isAuthenticated: true,
					isLoading: false,
				});
			},

			logout: () => {
				set({
					user: null,
					token: null,
					isAuthenticated: false,
					isLoading: false,
				});
			},

			initialize: () => {
				const { token, user } = get();
				if (token && user) {
					set({
						isAuthenticated: true,
						isLoading: false,
					});
				} else {
					set({
						isAuthenticated: false,
						isLoading: false,
					});
				}
			},
		}),
		{
			name: "auth-storage",
			storage: createJSONStorage(() => localStorage),
			// Only persist user and token, not loading states
			partialize: (state) => ({
				user: state.user,
				token: state.token,
			}),
			// Initialize authentication state after hydration
			onRehydrateStorage: () => (state) => {
				if (state) {
					state.initialize();
				}
			},
		},
	),
);

// Selectors for better performance
export const useAuth = () => useAuthStore((state) => state);
export const useUser = () => useAuthStore((state) => state.user);
export const useToken = () => useAuthStore((state) => state.token);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useIsLoading = () => useAuthStore((state) => state.isLoading);
