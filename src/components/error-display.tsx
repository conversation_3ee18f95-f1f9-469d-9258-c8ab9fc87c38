import { AlertCircle, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface ErrorDisplayProps {
	error: Error | unknown;
	onRetry?: () => void;
	className?: string;
	title?: string;
	description?: string;
	showDetails?: boolean;
}

export function ErrorDisplay({
	error,
	onRetry,
	className,
	title = "Something went wrong",
	description = "An error occurred while loading this content.",
	showDetails = false,
}: ErrorDisplayProps) {
	const errorMessage = error instanceof Error ? error.message : String(error);

	return (
		<Card className={cn("w-full", className)}>
			<CardHeader>
				<div className="flex items-center gap-2">
					<AlertCircle className="h-5 w-5 text-destructive" />
					<CardTitle className="text-destructive">{title}</CardTitle>
				</div>
				<CardDescription>{description}</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				{showDetails && (
					<div className="p-3 bg-muted rounded-md">
						<p className="text-sm font-mono text-muted-foreground">
							{errorMessage}
						</p>
					</div>
				)}
				{onRetry && (
					<Button onClick={onRetry} variant="outline" className="w-full">
						<RefreshCw className="h-4 w-4 mr-2" />
						Try Again
					</Button>
				)}
			</CardContent>
		</Card>
	);
}

interface InlineErrorProps {
	error: Error | unknown;
	onRetry?: () => void;
	className?: string;
}

export function InlineError({ error, onRetry, className }: InlineErrorProps) {
	const errorMessage = error instanceof Error ? error.message : String(error);

	return (
		<div
			className={cn(
				"flex items-center justify-between p-3 bg-destructive/10 border border-destructive/20 rounded-md",
				className,
			)}
		>
			<div className="flex items-center gap-2">
				<AlertCircle className="h-4 w-4 text-destructive" />
				<span className="text-sm text-destructive">{errorMessage}</span>
			</div>
			{onRetry && (
				<Button onClick={onRetry} variant="ghost" size="sm">
					<RefreshCw className="h-3 w-3" />
				</Button>
			)}
		</div>
	);
}

interface PageErrorProps {
	error: Error | unknown;
	onRetry?: () => void;
	title?: string;
	description?: string;
}

export function PageError({
	error,
	onRetry,
	title = "Page Error",
	description = "Something went wrong while loading this page.",
}: PageErrorProps) {
	return (
		<div className="min-h-screen flex items-center justify-center p-4">
			<ErrorDisplay
				error={error}
				onRetry={onRetry}
				title={title}
				description={description}
				showDetails={process.env.NODE_ENV === "development"}
				className="max-w-md"
			/>
		</div>
	);
}
