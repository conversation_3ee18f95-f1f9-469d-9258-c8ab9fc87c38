# Pukpara API Documentation

## Overview

The Pukpara Application API is a backend service for the PCG (Pukpara Community Ghana) application. This RESTful API provides user authentication, role-based access control (RBAC), user management, location services, and tenant management.

**Note**: This documentation reflects the current state of the backend API as defined in the Swagger specification. Some endpoints may have limited response documentation due to incomplete Swagger definitions.

## Base URL

```
http://pukparabootrapper-dev.eba-344m2e93.us-east-1.elasticbeanstalk.com
```

## Authentication

The API uses JWT (JSON Web Token) based authentication. After successful login, you'll receive an access token that must be included in subsequent requests.

### Authentication Header
```
Authorization: Bearer <your_access_token>
```

## Response Formats

Based on the schema definitions, the API uses different response formats:

### Login Response
```json
{
  "message": "string",
  "statusCode": 200,
  "token": "string",
  "userName": "string",
  "userId": "string",
  "modules": ["string"],
  "tenantId": "string"
}
```

### Standard Response
```json
{
  "message": "string",
  "statusCode": 200
}
```

### Error Response (PukparaException)
```json
{
  "statusCode": 400,
  "message": "Error description",
  "details": "Additional error details"
}
```

## HTTP Status Codes

- `200 OK` - Request successful
- `400 Bad Request` - Invalid request data
- `500 Internal Server Error` - Server error

---

## Authentication Endpoints

### 1. User Login

**POST** `/api/auth/login`

Authenticate a user and receive access token.

#### Request Body (UserLoginRequest)
```json
{
  "username": "string",
  "password": "string"
}
```

#### Response (200 OK) - LoginResponse
```json
{
  "message": "string",
  "statusCode": 200,
  "token": "string",
  "userName": "string",
  "userId": "string",
  "modules": ["string"],
  "tenantId": "string"
}
```

#### Error Responses
- **400 Bad Request** - PukparaException
- **500 Internal Server Error** - PukparaException

### 2. User Registration

**POST** `/api/auth/register`

Register a new user account.

#### Request Body (UserRegistrationRequest)
```json
{
  "firstname": "string",
  "lastname": "string",
  "email": "string",
  "phoneNumber": "string",
  "districtId": "string",
  "address": "string",
  "password": "string",
  "confirmPassword": "string"
}
```

#### Response (200 OK) - Response
```json
{
  "message": "string",
  "statusCode": 200
}
```

#### Error Responses
- **400 Bad Request** - PukparaException
- **500 Internal Server Error** - PukparaException

### 3. Request Password Reset

**POST** `/api/auth/reset-password/{email}`

Request a password reset for the specified email address.

#### Path Parameters
- `email` (string, required): The email address of the user

#### Response (200 OK) - Response
```json
{
  "message": "string",
  "statusCode": 200
}
```

#### Error Responses
- **400 Bad Request** - PukparaException
- **500 Internal Server Error** - PukparaException

### 4. Validate Password Reset

**POST** `/api/auth/reset-password/validate`

Reset password using the token from the reset email.

#### Request Body (ResetPasswordRequest)
```json
{
  "token": "string",
  "password": "string"
}
```

#### Response (200 OK) - Response
```json
{
  "message": "string",
  "statusCode": 200
}
```

#### Error Responses
- **400 Bad Request** - PukparaException
- **500 Internal Server Error** - PukparaException

### 5. Change Password

**PUT** `/api/auth/change-password`

Change the current user's password (requires authentication).

#### Request Body (ChangePasswordRequest)
```json
{
  "oldPassword": "string",
  "newPassword": "string"
}
```

#### Response (200 OK) - Response
```json
{
  "message": "string",
  "statusCode": 200
}
```

#### Error Responses
- **400 Bad Request** - PukparaException
- **500 Internal Server Error** - PukparaException

---

## Location Services Endpoints

### 1. Get Districts (Paginated)

**POST** `/api/locations/districts`

Get districts with pagination and search functionality.

#### Request Body (PagedSearchFilter)
```json
{
  "pageNumber": 0,
  "pageSize": 0,
  "searchQuery": "string"
}
```

#### Response (200 OK)
Response format not specified in Swagger.

### 2. Get All Regions

**GET** `/api/locations/regions`

Get all available regions.

#### Response (200 OK)
Response format not specified in Swagger.

### 3. Get Districts by Region

**GET** `/api/locations/regions/{regionId}/districts`

Get all districts within a specific region.

#### Path Parameters
- `regionId` (string, required): The ID of the region

#### Response (200 OK)
Response format not specified in Swagger.

---

## User Management Endpoints

### 1. Get All Users

**GET** `/api/users`

Get a list of all users.

#### Response (200 OK)
Response format not specified in Swagger.

### 2. Create User

**POST** `/api/users`

Create a new user.

#### Request Body (CreateUserRequest)
```json
{
  "firstname": "string",
  "lastname": "string",
  "email": "string",
  "phoneNumber": "string",
  "roleId": "string",
  "password": "string",
  "address": "string",
  "districtId": "string"
}
```

#### Response (200 OK)
Response format not specified in Swagger.

### 3. Update User Status

**PATCH** `/api/users/{userId}/status`

Update a user's active/inactive status.

#### Path Parameters
- `userId` (string, required): The ID of the user to update

#### Request Body (UpdateUserStatusRequest)
```json
{
  "isEnabled": true
}
```

#### Response (200 OK)
Response format not specified in Swagger.

### 4. Assign Role to User

**POST** `/api/users/{userId}/roles/{roleId}`

Assign a role to a user.

#### Path Parameters
- `userId` (string, required): The ID of the user
- `roleId` (string, required): The ID of the role to assign

#### Response (200 OK)
Response format not specified in Swagger.

### 5. Remove Role from User

**DELETE** `/api/users/{userId}/roles`

Remove a role from a user.

#### Path Parameters
- `userId` (string, required): The ID of the user

#### Request Body
```json
"string"
```

#### Response (200 OK)
Response format not specified in Swagger.

### 6. Get User Permissions

**GET** `/api/users/{userId}/permissions`

Get all permissions assigned to a user.

#### Path Parameters
- `userId` (string, required): The ID of the user

#### Response (200 OK)
Response format not specified in Swagger.

### 7. Assign Permissions to User

**POST** `/api/users/{userId}/permissions`

Assign permissions directly to a user.

#### Path Parameters
- `userId` (string, required): The ID of the user

#### Request Body (Array of AppPermission)
```json
[
  {
    "id": "string",
    "name": "string",
    "description": "string"
  }
]
```

#### Response (200 OK)
Response format not specified in Swagger.

### 8. Remove Permissions from User

**DELETE** `/api/users/{userId}/permissions`

Remove permissions from a user.

#### Path Parameters
- `userId` (string, required): The ID of the user

#### Request Body (Array of strings)
```json
["string"]
```

#### Response (200 OK)
Response format not specified in Swagger.

---

## Permissions Management Endpoints

### 1. Get All Permissions

**GET** `/api/permissions`

Get all available permissions with pagination.

#### Query Parameters
- `PageNumber` (integer, optional): Page number for pagination
- `PageSize` (integer, optional): Number of items per page
- `SearchQuery` (string, optional): Search term for filtering permissions

#### Response (200 OK)
Response format not specified in Swagger.

### 2. Update Permission Description

**PATCH** `/api/permissions/{permissionId}/description`

Update the description of a permission.

#### Path Parameters
- `permissionId` (string, required): The ID of the permission

#### Request Body (UpdatePermissionDescriptionRequest)
```json
{
  "description": "string"
}
```

#### Response (200 OK)
Response format not specified in Swagger.

---

## Role Management Endpoints

### 1. Get All Roles

**GET** `/api/roles`

Get all available roles in the system.

#### Response (200 OK)
Response format not specified in Swagger.

### 2. Create Role

**POST** `/api/roles`

Create a new role.

#### Request Body (CreateRoleRequest)
```json
{
  "name": "string",
  "description": "string"
}
```

#### Response (200 OK)
Response format not specified in Swagger.

### 3. Get Role by ID

**GET** `/api/roles/{roleId}`

Get details of a specific role.

#### Path Parameters
- `roleId` (string, required): The ID of the role

#### Response (200 OK)
Response format not specified in Swagger.

### 4. Update Role

**PUT** `/api/roles/{roleId}`

Update an existing role.

#### Path Parameters
- `roleId` (string, required): The ID of the role

#### Request Body (UpdateRoleRequest)
```json
{
  "description": "string",
  "name": "string"
}
```

#### Response (200 OK)
Response format not specified in Swagger.

### 5. Delete Role

**DELETE** `/api/roles/{roleId}`

Delete a role from the system.

#### Path Parameters
- `roleId` (string, required): The ID of the role

#### Response (200 OK)
Response format not specified in Swagger.

### 6. Assign User to Role

**POST** `/api/roles/{roleId}/users/{userId}`

Assign a user to a role.

#### Path Parameters
- `userId` (string, required): The ID of the user
- `roleId` (string, required): The ID of the role

#### Response (200 OK)
Response format not specified in Swagger.

### 7. Remove User from Role

**DELETE** `/api/roles/{roleId}/users/{userId}`

Remove a user from a role.

#### Path Parameters
- `userId` (string, required): The ID of the user
- `roleId` (string, required): The ID of the role

#### Response (200 OK)
Response format not specified in Swagger.

### 8. Get User Roles

**GET** `/api/roles/user/{userId}`

Get all roles assigned to a specific user.

#### Path Parameters
- `userId` (string, required): The ID of the user

#### Response (200 OK)
Response format not specified in Swagger.

### 9. Get Role Permissions

**GET** `/api/roles/{roleId}/permissions`

Get all permissions assigned to a role.

#### Path Parameters
- `roleId` (string, required): The ID of the role

#### Response (200 OK)
Response format not specified in Swagger.

### 10. Add Permissions to Role

**POST** `/api/roles/{roleId}/permissions`

Add permissions to a role.

#### Path Parameters
- `roleId` (string, required): The ID of the role

#### Request Body (AddPermmissionsToRoleRequest)
```json
{
  "permissionIds": ["string"]
}
```

#### Response (200 OK)
Response format not specified in Swagger.

---

## Tenant Management Endpoints (Admin)

### 1. Get All Tenants

**GET** `/api/admin/tenants`

Get all tenants in the system (super admin function).

#### Query Parameters
- `Status` (string, optional): Filter by tenant status
- `PageNumber` (integer, optional): Page number
- `PageSize` (integer, optional): Items per page
- `SearchQuery` (string, optional): Search term

#### Response (200 OK)
Response format not specified in Swagger.

### 2. Update Tenant

**POST** `/api/admin/tenants/{tenantId}`

Update tenant information.

#### Path Parameters
- `tenantId` (string, required): The ID of the tenant

#### Request Body (UpdateTenantRequest)
```json
{
  "adminUserId": "string",
  "workspaceName": "string"
}
```

#### Response (200 OK)
Response format not specified in Swagger.

### 3. Update Tenant Status

**PATCH** `/api/admin/tenants/{tenantId}/status`

Update the status of a tenant.

#### Path Parameters
- `tenantId` (string, required): The ID of the tenant

#### Request Body (UpdateTenantStatusRequest)
```json
{
  "status": "string",
  "disablingReasons": "string"
}
```

#### Response (200 OK)
Response format not specified in Swagger.

### 4. Get Tenant Users

**GET** `/api/admin/tenants/{tenantId}/users`

Get all users within a specific tenant.

#### Path Parameters
- `tenantId` (string, required): The ID of the tenant

#### Query Parameters
- `PageNumber` (integer, optional): Page number
- `PageSize` (integer, optional): Items per page
- `SearchQuery` (string, optional): Search term

#### Response (200 OK)
Response format not specified in Swagger.

### 5. Create User in Tenant

**POST** `/api/admin/tenants/{tenantId}/users`

Create a new user within a specific tenant.

#### Path Parameters
- `tenantId` (string, required): The ID of the tenant

#### Request Body (CreateUserRequest)
```json
{
  "firstname": "string",
  "lastname": "string",
  "email": "string",
  "phoneNumber": "string",
  "roleId": "string",
  "password": "string",
  "address": "string",
  "districtId": "string"
}
```

#### Response (200 OK)
Response format not specified in Swagger.

### 6. Delete User from Tenant

**DELETE** `/api/admin/tenants/{tenantId}/users/{userId}`

Remove a user from a tenant.

#### Path Parameters
- `tenantId` (string, required): The ID of the tenant
- `userId` (string, required): The ID of the user

#### Response (200 OK)
Response format not specified in Swagger.

### 7. Delegate Access

**POST** `/api/admin/tenants/{tenantId}/users/{userId}/delegate_access`

Delegate administrative access to a user within a tenant.

#### Path Parameters
- `tenantId` (string, required): The ID of the tenant
- `userId` (string, required): The ID of the user

#### Response (200 OK)
Response format not specified in Swagger.

---

## Important Notes

### Current API State

This documentation reflects the **exact current state** of the backend API as defined in the Swagger specification. Please note:

1. **Limited Response Documentation**: Many endpoints do not specify response formats in the Swagger definition, indicated by "Response format not specified in Swagger."

2. **Authentication**: Most endpoints likely require authentication, but this is not explicitly documented in the Swagger specification.

3. **Error Handling**: The API uses PukparaException for error responses with the following structure:
   ```json
   {
     "statusCode": 400,
     "message": "Error description",
     "details": "Additional error details"
   }
   ```

4. **Missing Features**: Some common authentication features are not implemented:
   - No refresh token endpoint
   - No email confirmation endpoint
   - Limited user profile management

### For Frontend Developers

When integrating with these endpoints:
- Test each endpoint to understand the actual response format
- Handle cases where response formats may differ from expectations
- Implement proper error handling for PukparaException responses
- Be prepared for authentication requirements on most endpoints

### Schema References

The API uses the following main request/response schemas:
- `UserLoginRequest`: `{ username, password }`
- `UserRegistrationRequest`: `{ firstname, lastname, email, phoneNumber, districtId, address, password, confirmPassword }`
- `LoginResponse`: `{ message, statusCode, token, userName, userId, modules, tenantId }`
- `CreateUserRequest`: `{ firstname, lastname, email, phoneNumber, roleId, password, address, districtId }`
- `PagedSearchFilter`: `{ pageNumber, pageSize, searchQuery }`
- `AppPermission`: `{ id, name, description }`

