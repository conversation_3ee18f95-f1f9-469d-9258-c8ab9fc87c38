"use client";

import { useState } from "react";
import { AuthSignupLayout } from "@/components/auth/auth-signup-layout";
import { RegisterForm } from "@/components/auth/register-form";

export default function SignUpPage() {
	const [currentStep, setCurrentStep] = useState(1);

	const handleBackClick = () => {
		setCurrentStep(1);
	};

	return (
		<AuthSignupLayout currentStep={currentStep} onBackClick={handleBackClick}>
			<RegisterForm currentStep={currentStep} onStepChange={setCurrentStep} />
		</AuthSignupLayout>
	);
}
