import { apiClient } from "./client";
import type { DistrictsRequest, DistrictsResponse } from "./types";

export const districtsApi = {
	// Get all districts with pagination
	getAll: async (params: DistrictsRequest): Promise<DistrictsResponse> => {
		return apiClient.post<DistrictsResponse>("/api/locations/districts", params);
	},

	// Get districts with default pagination (convenience method)
	getList: async (
		pageNumber = 1,
		pageSize = 300,
	): Promise<DistrictsResponse> => {
		return districtsApi.getAll({ pageNumber, pageSize });
	},
};
