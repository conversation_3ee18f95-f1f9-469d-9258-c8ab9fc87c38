"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod/v3";
import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { CheckEmail } from "./check-email";
import { useForgotPassword } from "@/lib/hooks";

const formSchema = z.object({
	email: z
		.string()
		.min(1, {
			message: "Email is required.",
		})
		.email({
			message: "Please enter a valid email address.",
		}),
});

export function ForgotPasswordForm() {
	const [isSubmitted, setIsSubmitted] = useState(false);
	const [submittedEmail, setSubmittedEmail] = useState("");
	const forgotPasswordMutation = useForgotPassword();

	// 1. Define your form.
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: "",
		},
	});

	// 2. Define a submit handler.
	function onSubmit(values: z.infer<typeof formSchema>) {
		forgotPasswordMutation.mutate(
			{ email: values.email },
			{
				onSuccess: () => {
					setSubmittedEmail(values.email);
					setIsSubmitted(true);
				},
			},
		);
	}

	// Show check email component after submission
	if (isSubmitted) {
		return <CheckEmail email={submittedEmail} />;
	}

	return (
		<Card className="border-0 shadow-none mt-2">
			<CardHeader className="space-y-1 text-left lg:text-left p-0">
				<CardTitle className="text-lg font-semibold text-foreground leading-7">
					Forgot password?
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5">
					No worries, we'll send you reset instructions.
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-2">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
						{/* Email Field */}
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Email
									</FormLabel>
									<FormControl>
										<Input
											type="email"
											placeholder="<EMAIL>"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Reset Password Button */}
						<Button type="submit" className="w-full" size="lg">
							Reset password
						</Button>
					</form>
				</Form>

				{/* Back to Sign In Link */}
				<div className="mt-6 text-center">
					<span className="text-sm text-muted-foreground">
						<Link
							href="/sign-in"
							className="text-primary hover:underline font-medium"
						>
							← Back to Sign In
						</Link>
					</span>
				</div>
			</CardContent>
		</Card>
	);
}
