import { useAuthStore } from "@/lib/store";
import type { ApiError } from "./types";

const API_BASE_URL =
	"http://pukparabootrapper-dev.eba-344m2e93.us-east-1.elasticbeanstalk.com";

export class ApiClient {
	private baseUrl: string;

	constructor(baseUrl: string) {
		this.baseUrl = baseUrl;
	}

	private getAuthToken(): string | null {
		// Get token from Zustand store
		return useAuthStore.getState().token;
	}

	private async request<T>(
		endpoint: string,
		options: RequestInit = {},
	): Promise<T> {
		const url = `${this.baseUrl}${endpoint}`;
		const token = this.getAuthToken();

		const config: RequestInit = {
			headers: {
				"Content-Type": "application/json",
				...(token && { Authorization: `Bearer ${token}` }),
				...options.headers,
			},
			...options,
			// Add timeout using AbortSignal
			signal: AbortSignal.timeout(10000), // 10 second timeout
		};

		try {
			const response = await fetch(url, config);

			// Handle non-JSON responses
			const contentType = response.headers.get("content-type");
			if (!contentType?.includes("application/json")) {
				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}
				// For non-JSON success responses, return empty object
				return {} as T;
			}

			const data = await response.json();

			if (!response.ok) {
				// Handle API errors
				const error: ApiError = {
					message: data.message || `HTTP ${response.status}: ${response.statusText}`,
					statusCode: response.status,
					details: data.details,
				};
				throw error;
			}

			return data;
		} catch (error) {
			// Handle network errors, timeouts, etc.
			if (error instanceof Error) {
				if (error.name === "AbortError") {
					throw new Error("Request timeout");
				}
				if (error.name === "TypeError" && error.message.includes("fetch")) {
					throw new Error("Network error - please check your connection");
				}
			}
			// Re-throw API errors as-is
			throw error;
		}
	}

	// HTTP methods
	async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
		return this.request<T>(endpoint, { method: "GET", ...options });
	}

	async post<T>(
		endpoint: string,
		data?: any,
		options?: RequestInit,
	): Promise<T> {
		return this.request<T>(endpoint, {
			method: "POST",
			body: data ? JSON.stringify(data) : undefined,
			...options,
		});
	}

	async put<T>(
		endpoint: string,
		data?: any,
		options?: RequestInit,
	): Promise<T> {
		return this.request<T>(endpoint, {
			method: "PUT",
			body: data ? JSON.stringify(data) : undefined,
			...options,
		});
	}

	async patch<T>(
		endpoint: string,
		data?: any,
		options?: RequestInit,
	): Promise<T> {
		return this.request<T>(endpoint, {
			method: "PATCH",
			body: data ? JSON.stringify(data) : undefined,
			...options,
		});
	}

	async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
		return this.request<T>(endpoint, { method: "DELETE", ...options });
	}
}

// Create and export the main API client instance
export const apiClient = new ApiClient(API_BASE_URL);
