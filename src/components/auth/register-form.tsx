"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod/v3";

import { DistrictCombobox } from "@/components/auth/district-combobox";
import { useRegister } from "@/lib/hooks";
import type { RegisterRequest } from "@/lib/api/types";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";

// Base step 1 schema (without refinement for merging)
const step1BaseSchema = z.object({
	firstName: z.string().min(1, {
		message: "First name is required.",
	}),
	lastName: z.string().min(1, {
		message: "Last name is required.",
	}),
	email: z
		.string()
		.min(1, {
			message: "Email is required.",
		})
		.email({
			message: "Please enter a valid email address.",
		}),
	password: z
		.string()
		.min(1, {
			message: "Password is required.",
		})
		.min(4, {
			message: "Password must be at least 4 characters.",
		}),
	confirmPassword: z.string().min(1, {
		message: "Please confirm your password.",
	}),
});

// Step 1 schema with password confirmation validation
const step1Schema = step1BaseSchema.refine(
	(data) => data.password === data.confirmPassword,
	{
		message: "Passwords don't match.",
		path: ["confirmPassword"],
	},
);

// Step 2 schema
const step2Schema = z.object({
	phoneNumber: z.string().min(1, {
		message: "Phone number is required.",
	}),
	district: z.string().min(1, {
		message: "District is required.",
	}),
	address: z.string().min(1, {
		message: "Address is required.",
	}),
	acceptTerms: z.boolean().refine((val) => val === true, {
		message: "You must accept the terms of service to continue.",
	}),
});

// Combined schema for final submission
const completeSchema = step1BaseSchema
	.merge(step2Schema)
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords don't match.",
		path: ["confirmPassword"],
	});

interface RegisterFormProps {
	currentStep?: number;
	onStepChange?: (step: number) => void;
}

export function RegisterForm({ currentStep, onStepChange }: RegisterFormProps) {
	const registerMutation = useRegister();
	const [internalStep, setInternalStep] = useState(1);

	// Use external step if provided, otherwise use internal step
	const activeStep = currentStep ?? internalStep;
	const handleStepChange = onStepChange || setInternalStep;

	// Step 1 form
	const step1Form = useForm<z.infer<typeof step1BaseSchema>>({
		resolver: zodResolver(step1Schema),
		defaultValues: {
			firstName: "",
			lastName: "",
			email: "",
			password: "",
			confirmPassword: "",
		},
	});

	// Step 2 form
	const step2Form = useForm<z.infer<typeof step2Schema>>({
		resolver: zodResolver(step2Schema),
		defaultValues: {
			phoneNumber: "",
			district: "",
			address: "",
			acceptTerms: false,
		},
	});

	// Handle step 1 submission
	function onStep1Submit(values: z.infer<typeof step1BaseSchema>) {
		console.log("Step 1 submitted:", values);
		handleStepChange(2);
	}

	// Handle step 2 submission (final submission)
	function onStep2Submit(values: z.infer<typeof step2Schema>) {
		const step1Data = step1Form.getValues();
		const completeData: z.infer<typeof completeSchema> = {
			...step1Data,
			...values,
		};

		// Map form data to API format
		const registerData: RegisterRequest = {
			firstname: completeData.firstName,
			lastname: completeData.lastName,
			email: completeData.email,
			phoneNumber: completeData.phoneNumber,
			districtId: completeData.district,
			address: completeData.address,
			password: completeData.password,
			confirmPassword: completeData.confirmPassword,
		};

		registerMutation.mutate(registerData);
	}

	return (
		<Card className="border-0 shadow-none mt-8">
			<CardHeader className="space-y-2 text-left lg:text-left p-0">
				{/* Step indicator */}
				<Badge variant="default" className="mb-1 w-fit">
					Step {activeStep} of 2
				</Badge>
				<CardTitle className="text-xl font-semibold text-foreground leading-7">
					{activeStep === 1
						? "Create your Pukpara account"
						: "Complete your profile"}
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5">
					{activeStep === 1
						? "Join thousands of farmers transforming agriculture with digital tools"
						: "Help us connect with you and personalize your farming experience"}
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-6">
				{activeStep === 1 ? (
					<Form {...step1Form}>
						<form
							onSubmit={step1Form.handleSubmit(onStep1Submit)}
							className="space-y-5"
						>
							{/* Name Fields */}
							<div className="grid grid-cols-2 gap-4">
								<FormField
									control={step1Form.control}
									name="firstName"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium text-foreground">
												First name
											</FormLabel>
											<FormControl>
												<Input placeholder="John" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={step1Form.control}
									name="lastName"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium text-foreground">
												Last name
											</FormLabel>
											<FormControl>
												<Input placeholder="Doe" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Email Field */}
							<FormField
								control={step1Form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium text-foreground">
											Email
										</FormLabel>
										<FormControl>
											<Input
												type="email"
												placeholder="<EMAIL>"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Password Fields */}
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={step1Form.control}
									name="password"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium text-foreground">
												Password
											</FormLabel>
											<FormControl>
												<Input
													type="password"
													placeholder="Enter your password"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={step1Form.control}
									name="confirmPassword"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium text-foreground">
												Confirm Password
											</FormLabel>
											<FormControl>
												<Input
													type="password"
													placeholder="Confirm your password"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Continue Button */}
							<Button type="submit" className="w-full mt-6" size="lg">
								Continue
							</Button>
						</form>
					</Form>
				) : (
					<Form {...step2Form}>
						<form
							onSubmit={step2Form.handleSubmit(onStep2Submit)}
							className="space-y-5"
						>
							{/* Phone Number Field */}
							<FormField
								control={step2Form.control}
								name="phoneNumber"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium text-foreground">
											Phone number
										</FormLabel>
										<FormControl>
											<PhoneInput
												defaultCountry="GH"
												placeholder="55 000 0000"
												value={field.value}
												onChange={field.onChange}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* District Field */}
							<DistrictCombobox
								control={step2Form.control}
								name="district"
								label="District"
								placeholder="Select your district"
							/>

							{/* Address Field */}
							<FormField
								control={step2Form.control}
								name="address"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium text-foreground">
											Address
										</FormLabel>
										<FormControl>
											<Input
												placeholder="e.g., House number, street name, area"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Terms of Service */}
							<FormField
								control={step2Form.control}
								name="acceptTerms"
								render={({ field }) => (
									<FormItem className="flex flex-row items-start space-x-3 space-y-0 border rounded-lg p-4 bg-muted/50">
										<FormControl>
											<Checkbox
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel className="text-sm font-normal cursor-pointer">
												I agree to the{" "}
												<Link
													href="/terms"
													className="text-primary underline hover:no-underline"
												>
													Terms of Service
												</Link>{" "}
												and{" "}
												<Link
													href="/privacy"
													className="text-primary underline hover:no-underline"
												>
													Privacy Policy
												</Link>
											</FormLabel>
											<FormMessage />
										</div>
									</FormItem>
								)}
							/>

							{/* Create Account Button */}
							<Button
								type="submit"
								className="w-full mt-6"
								size="lg"
								disabled={registerMutation.isPending}
							>
								{registerMutation.isPending
									? "Creating Account..."
									: "Create Account"}
							</Button>
						</form>
					</Form>
				)}

				{/* Sign In Link */}
				<div className="mt-8 text-center">
					<span className="text-sm text-muted-foreground">
						Already have a Pukpara account?{" "}
						<Link
							href="/sign-in"
							className="text-primary hover:underline font-medium"
						>
							Sign In
						</Link>
					</span>
				</div>
			</CardContent>
		</Card>
	);
}
