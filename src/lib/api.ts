const API_BASE_URL =
	"http://pukparabootrapper-dev.eba-344m2e93.us-east-1.elasticbeanstalk.com";

export interface LoginRequest {
	username: string;
	password: string;
}

export interface LoginResponse {
	token: string;
	userName: string;
	userId: string;
	modules: string[];
	tenantId: string;
	message: string;
	statusCode: number;
}

export interface RegisterRequest {
	firstname: string;
	lastname: string;
	email: string;
	phoneNumber: string;
	districtId: string;
	address: string;
	password: string;
	confirmPassword: string;
}

export interface RegisterResponse {
	message: string;
	statusCode: number;
}

export interface ApiError {
	message: string;
	statusCode: number;
	details?: string;
}

export interface District {
	id: string;
	name: string;
	region: string | null;
}

export interface DistrictsRequest {
	pageNumber: number;
	pageSize: number;
}

export interface DistrictsResponse {
	data: District[];
	message: string | null;
	statusCode: number;
}

class ApiClient {
	private baseUrl: string;

	constructor(baseUrl: string) {
		this.baseUrl = baseUrl;
	}

	private async request<T>(
		endpoint: string,
		options: RequestInit = {},
	): Promise<T> {
		const url = `${this.baseUrl}${endpoint}`;

		const config: RequestInit = {
			headers: {
				"Content-Type": "application/json",
				...options.headers,
			},
			...options,
			// Add timeout using AbortSignal
			signal: AbortSignal.timeout(10000), // 10 second timeout
		};

		try {
			const response = await fetch(url, config);
			const data: unknown = await response.json();

			if (!response.ok) {
				const errorData = data as {
					message?: string;
					statusCode?: number;
					details?: string;
				};
				const error: ApiError = {
					message: errorData.message || "An error occurred",
					statusCode: errorData.statusCode || response.status,
					details: errorData.details,
				};
				throw error;
			}

			return data as T;
		} catch (error) {
			if (error instanceof Error) {
				if (error.name === "TimeoutError") {
					throw new Error("Request timeout - please try again");
				}
				if (error.name === "AbortError") {
					throw new Error("Request was cancelled");
				}
			}
			throw error;
		}
	}

	async login(credentials: LoginRequest): Promise<LoginResponse> {
		return this.request<LoginResponse>("/api/auth/login", {
			method: "POST",
			body: JSON.stringify(credentials),
		});
	}

	async register(userData: RegisterRequest): Promise<RegisterResponse> {
		return this.request<RegisterResponse>("/api/auth/register", {
			method: "POST",
			body: JSON.stringify(userData),
		});
	}

	async getDistricts(request: DistrictsRequest): Promise<DistrictsResponse> {
		return this.request<DistrictsResponse>("/api/locations/districts", {
			method: "POST",
			body: JSON.stringify(request),
		});
	}
}

export const apiClient = new ApiClient(API_BASE_URL);

// Auth utilities
export const auth = {
	login: (credentials: LoginRequest) => apiClient.login(credentials),
	register: (userData: RegisterRequest) => apiClient.register(userData),

	getToken: () => {
		if (typeof window !== "undefined") {
			return localStorage.getItem("auth_token");
		}
		return null;
	},

	setToken: (token: string) => {
		if (typeof window !== "undefined") {
			localStorage.setItem("auth_token", token);
		}
	},

	removeToken: () => {
		if (typeof window !== "undefined") {
			localStorage.removeItem("auth_token");
		}
	},

	getUserData: () => {
		if (typeof window !== "undefined") {
			const userData = localStorage.getItem("user_data");
			return userData ? JSON.parse(userData) : null;
		}
		return null;
	},

	setUserData: (userData: Omit<LoginResponse, "message" | "statusCode">) => {
		if (typeof window !== "undefined") {
			localStorage.setItem("user_data", JSON.stringify(userData));
		}
	},

	clearUserData: () => {
		if (typeof window !== "undefined") {
			localStorage.removeItem("user_data");
		}
	},

	isAuthenticated: () => {
		return !!auth.getToken();
	},
};

// Districts utilities
export const districts = {
	getAll: (pageNumber = 1, pageSize = 300) =>
		apiClient.getDistricts({ pageNumber, pageSize }),
};
