"use client";

import { CheckIcon, ChevronsUpDownIcon } from "lucide-react";
import * as React from "react";
import type { Control, FieldPath, FieldValues } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useDistrictOptions } from "@/lib/hooks";

type DistrictOption = {
	value: string;
	label: string;
	region: string | null;
};

interface DistrictComboboxProps<
	TFieldValues extends FieldValues = FieldValues,
	TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
	control: Control<TFieldValues>;
	name: TName;
	label: string;
	placeholder: string;
}

export function DistrictCombobox<
	TFieldValues extends FieldValues = FieldValues,
	TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
	control,
	name,
	label,
	placeholder,
}: DistrictComboboxProps<TFieldValues, TName>) {
	const [open, setOpen] = React.useState(false);
	const triggerRef = React.useRef<HTMLButtonElement>(null);
	const { options: districtsData, isLoading: loading } = useDistrictOptions();

	return (
		<FormField
			control={control}
			name={name}
			render={({ field }) => (
				<FormItem>
					<FormLabel className="text-sm font-medium text-foreground">
						{label}
					</FormLabel>
					<Popover open={open} onOpenChange={setOpen}>
						<PopoverTrigger asChild>
							<FormControl>
								<Button
									ref={triggerRef}
									variant="outline"
									aria-expanded={open}
									className="w-full justify-between"
									disabled={loading}
								>
									{field.value
										? districtsData.find(
												(district: DistrictOption) =>
													district.value === field.value,
											)?.label
										: loading
											? "Loading districts..."
											: placeholder}
									<ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
								</Button>
							</FormControl>
						</PopoverTrigger>
						<PopoverContent
							className="p-0"
							style={{ width: triggerRef.current?.offsetWidth }}
						>
							<Command>
								<CommandInput
									placeholder={`Search ${label.toLowerCase()}...`}
								/>
								<CommandList>
									<CommandEmpty>
										{loading ? "Loading districts..." : "No district found."}
									</CommandEmpty>
									<CommandGroup>
										{districtsData.map((district: DistrictOption) => (
											<CommandItem
												key={district.value}
												value={district.value}
												onSelect={(currentValue) => {
													field.onChange(
														currentValue === field.value ? "" : currentValue,
													);
													setOpen(false);
												}}
											>
												<CheckIcon
													className={cn(
														"mr-2 h-4 w-4",
														field.value === district.value
															? "opacity-100"
															: "opacity-0",
													)}
												/>
												{district.label}
											</CommandItem>
										))}
									</CommandGroup>
								</CommandList>
							</Command>
						</PopoverContent>
					</Popover>
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}
