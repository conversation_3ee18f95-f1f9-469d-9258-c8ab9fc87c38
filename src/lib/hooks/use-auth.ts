import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { authApi } from "@/lib/api";
import { useAuthStore } from "@/lib/store";
import { queryKeys } from "@/lib/query-client";
import type {
	LoginRequest,
	RegisterRequest,
	ForgotPasswordRequest,
	ResetPasswordRequest,
} from "@/lib/api/types";

// Login mutation
export function useLogin() {
	const router = useRouter();
	const { login } = useAuthStore();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (credentials: LoginRequest) => authApi.login(credentials),
		onSuccess: (response) => {
			// Update Zustand store
			login(response.token, {
				userId: response.userId,
				userName: response.userName,
				tenantId: response.tenantId,
				modules: response.modules,
			});

			// Invalidate and refetch user-related queries
			queryClient.invalidateQueries({ queryKey: queryKeys.auth.all });

			// Show success message
			toast.success("Welcome back!", {
				description: "You have been successfully logged in.",
			});

			// Redirect to dashboard
			router.push("/dashboard");
		},
		onError: (error: any) => {
			console.error("Login error:", error);
			toast.error("Login failed", {
				description: error.message || "Please check your credentials and try again.",
			});
		},
	});
}

// Register mutation
export function useRegister() {
	const router = useRouter();

	return useMutation({
		mutationFn: (userData: RegisterRequest) => authApi.register(userData),
		onSuccess: (response) => {
			toast.success("Registration successful!", {
				description: "Please check your email to activate your account.",
			});
			// Navigate to account activation page
			router.push("/account-activation");
		},
		onError: (error: any) => {
			console.error("Registration error:", error);
			toast.error("Registration failed", {
				description: error.message || "Please try again.",
			});
		},
	});
}

// Forgot password mutation
export function useForgotPassword() {
	return useMutation({
		mutationFn: (data: ForgotPasswordRequest) => authApi.forgotPassword(data),
		onSuccess: () => {
			toast.success("Reset link sent!", {
				description: "Please check your email for password reset instructions.",
			});
		},
		onError: (error: any) => {
			console.error("Forgot password error:", error);
			toast.error("Failed to send reset link", {
				description: error.message || "Please try again.",
			});
		},
	});
}

// Reset password mutation
export function useResetPassword() {
	const router = useRouter();

	return useMutation({
		mutationFn: (data: ResetPasswordRequest) => authApi.resetPassword(data),
		onSuccess: () => {
			toast.success("Password reset successful!", {
				description: "You can now log in with your new password.",
			});
			router.push("/sign-in");
		},
		onError: (error: any) => {
			console.error("Reset password error:", error);
			toast.error("Password reset failed", {
				description: error.message || "Please try again.",
			});
		},
	});
}

// Logout mutation
export function useLogout() {
	const router = useRouter();
	const { logout } = useAuthStore();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async () => {
			// Try to call server logout endpoint if it exists
			try {
				await authApi.logout();
			} catch (error) {
				// Ignore server logout errors, still logout locally
				console.warn("Server logout failed, continuing with local logout");
			}
		},
		onSuccess: () => {
			// Clear Zustand store
			logout();

			// Clear all queries
			queryClient.clear();

			// Show success message
			toast.success("Logged out successfully");

			// Redirect to sign-in
			router.push("/sign-in");
		},
		onError: (error: any) => {
			console.error("Logout error:", error);
			// Still logout locally even if server logout fails
			logout();
			queryClient.clear();
			router.push("/sign-in");
		},
	});
}

// Session query (for checking authentication status)
export function useSession() {
	const { user, token, isAuthenticated, isLoading } = useAuthStore();

	return useQuery({
		queryKey: queryKeys.auth.session(),
		queryFn: async () => {
			// Return current session data
			return { user, token, isAuthenticated };
		},
		enabled: !!token, // Only run if we have a token
		staleTime: 5 * 60 * 1000, // 5 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes
		initialData: { user, token, isAuthenticated },
	});
}

// Hook to get current user
export function useCurrentUser() {
	const { user } = useAuthStore();
	return user;
}

// Hook to check if user is authenticated
export function useIsAuthenticated() {
	const { isAuthenticated } = useAuthStore();
	return isAuthenticated;
}
