"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { auth, type LoginResponse } from "@/lib/api";

export interface User {
	userId: string;
	userName: string;
	tenantId: string;
	modules: string[];
}

export interface SessionData {
	user: User | null;
	token: string | null;
	isAuthenticated: boolean;
	isLoading: boolean;
}

interface AuthContextType extends SessionData {
	login: (
		token: string,
		userData: Omit<LoginResponse, "message" | "statusCode">,
	) => void;
	logout: () => void;
	refreshSession: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
	const [session, setSession] = useState<SessionData>({
		user: null,
		token: null,
		isAuthenticated: false,
		isLoading: true,
	});

	// Initialize session from localStorage
	useEffect(() => {
		const initializeSession = () => {
			try {
				const token = auth.getToken();
				const userData = auth.getUserData();

				if (token && userData) {
					setSession({
						user: {
							userId: userData.userId,
							userName: userData.userName,
							tenantId: userData.tenantId,
							modules: userData.modules,
						},
						token,
						isAuthenticated: true,
						isLoading: false,
					});
				} else {
					setSession({
						user: null,
						token: null,
						isAuthenticated: false,
						isLoading: false,
					});
				}
			} catch (error) {
				console.error("Failed to initialize session:", error);
				setSession({
					user: null,
					token: null,
					isAuthenticated: false,
					isLoading: false,
				});
			}
		};

		initializeSession();
	}, []);

	const login = (
		token: string,
		userData: Omit<LoginResponse, "message" | "statusCode">,
	) => {
		// Store in localStorage
		auth.setToken(token);
		auth.setUserData(userData);

		// Update session state
		setSession({
			user: {
				userId: userData.userId,
				userName: userData.userName,
				tenantId: userData.tenantId,
				modules: userData.modules,
			},
			token,
			isAuthenticated: true,
			isLoading: false,
		});
	};

	const logout = () => {
		// Clear localStorage
		auth.removeToken();
		auth.clearUserData();

		// Update session state
		setSession({
			user: null,
			token: null,
			isAuthenticated: false,
			isLoading: false,
		});
	};

	const refreshSession = () => {
		const token = auth.getToken();
		const userData = auth.getUserData();

		if (token && userData) {
			setSession({
				user: {
					userId: userData.userId,
					userName: userData.userName,
					tenantId: userData.tenantId,
					modules: userData.modules,
				},
				token,
				isAuthenticated: true,
				isLoading: false,
			});
		} else {
			logout();
		}
	};

	const value: AuthContextType = {
		...session,
		login,
		logout,
		refreshSession,
	};

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useSession() {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error("useSession must be used within an AuthProvider");
	}
	return context;
}

// Additional convenience hooks
export function useAuth() {
	return useSession();
}

export function useUser() {
	const { user } = useSession();
	return user;
}
