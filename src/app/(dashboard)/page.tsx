"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth, useLogout } from "@/lib/hooks";

export default function Home() {
	const { user, isAuthenticated, isLoading } = useAuth();
	const logoutMutation = useLogout();

	if (isLoading) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-background">
				<div className="text-center">
					<p className="text-lg text-muted-foreground">Loading...</p>
				</div>
			</div>
		);
	}

	if (!isAuthenticated) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-background">
				<div className="text-center space-y-6">
					<h1 className="text-4xl font-bold text-foreground">
						Pukpara back on next.js
					</h1>
					<p className="text-lg text-muted-foreground">
						Building authentication frontend...
					</p>
					<div className="space-x-4">
						<Button asChild>
							<Link href="/sign-in">Go to Sign In</Link>
						</Button>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-background p-6">
			<div className="max-w-4xl mx-auto space-y-6">
				{/* Header */}
				<div className="flex justify-between items-center">
					<div>
						<h1 className="text-3xl font-bold text-foreground">
							Welcome to Pukpara Dashboard
						</h1>
						<p className="text-muted-foreground">
							Hello, {user?.userName || "User"}!
						</p>
					</div>
					<Button
						variant="outline"
						onClick={() => logoutMutation.mutate()}
						disabled={logoutMutation.isPending}
					>
						{logoutMutation.isPending ? "Logging out..." : "Logout"}
					</Button>
				</div>

				{/* User Information Card */}
				<Card>
					<CardHeader>
						<CardTitle>User Information</CardTitle>
						<CardDescription>
							Your account details and permissions
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<div className="text-sm font-medium text-muted-foreground">
									User ID
								</div>
								<p className="text-sm font-mono bg-muted p-2 rounded">
									{user?.userId || "N/A"}
								</p>
							</div>
							<div>
								<div className="text-sm font-medium text-muted-foreground">
									Username
								</div>
								<p className="text-sm font-mono bg-muted p-2 rounded">
									{user?.userName || "N/A"}
								</p>
							</div>
							<div>
								<div className="text-sm font-medium text-muted-foreground">
									Tenant ID
								</div>
								<p className="text-sm font-mono bg-muted p-2 rounded">
									{user?.tenantId || "N/A"}
								</p>
							</div>
						</div>

						{/* Modules */}
						<div>
							<div className="text-sm font-medium text-muted-foreground">
								Modules/Permissions
							</div>
							<div className="flex flex-wrap gap-2 mt-2">
								{user?.modules && user.modules.length > 0 ? (
									user.modules.map((module) => (
										<Badge key={module} variant="secondary">
											{module}
										</Badge>
									))
								) : (
									<p className="text-sm text-muted-foreground">
										No modules assigned
									</p>
								)}
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Quick Actions */}
				<Card>
					<CardHeader>
						<CardTitle>Quick Actions</CardTitle>
						<CardDescription>Common tasks and navigation</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<Button variant="outline" className="h-20 flex-col">
								<span className="font-medium">Farm Management</span>
								<span className="text-xs text-muted-foreground">
									Manage your farm operations
								</span>
							</Button>
							<Button variant="outline" className="h-20 flex-col">
								<span className="font-medium">Crop Monitoring</span>
								<span className="text-xs text-muted-foreground">
									Track crop health and growth
								</span>
							</Button>
							<Button variant="outline" className="h-20 flex-col">
								<span className="font-medium">Reports</span>
								<span className="text-xs text-muted-foreground">
									View analytics and reports
								</span>
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
