{"openapi": "3.0.1", "info": {"title": "Pukpara Application API", "description": "PCG Application API Documentation", "version": "v1"}, "paths": {"/api/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UserLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UserLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UserLoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Responses.LoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Responses.LoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Responses.LoginResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}}}}, "/api/auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UserRegistrationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UserRegistrationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UserRegistrationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}}}}, "/api/auth/reset-password/{email}": {"post": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}}}}, "/api/auth/reset-password/validate": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}}}}, "/api/auth/change-password": {"put": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.ChangePasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.Response"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Exceptions.PukparaException"}}}}}}}, "/api/locations/districts": {"post": {"tags": ["Locations"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.PagedSearchFilter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.PagedSearchFilter"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.Shared.Models.DTOs.PagedSearchFilter"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/locations/regions/{regionId}/districts": {"get": {"tags": ["Locations"], "parameters": [{"name": "regionId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/locations/regions": {"get": {"tags": ["Locations"], "responses": {"200": {"description": "OK"}}}}, "/api/permissions": {"get": {"tags": ["Permissions"], "parameters": [{"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchQuery", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/permissions/{permissionId}/description": {"patch": {"tags": ["Permissions"], "parameters": [{"name": "permissionId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdatePermissionDescriptionRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdatePermissionDescriptionRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdatePermissionDescriptionRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/roles": {"get": {"tags": ["Roles"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Roles"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.CreateRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.CreateRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.CreateRoleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/roles/{roleId}": {"put": {"tags": ["Roles"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateRoleRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Roles"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Roles"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/roles/{roleId}/users/{userId}": {"post": {"tags": ["Roles"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Roles"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/roles/user/{userId}": {"get": {"tags": ["Roles"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/roles/{roleId}/permissions": {"get": {"tags": ["Roles"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Roles"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.AddPermmissionsToRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.AddPermmissionsToRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.AddPermmissionsToRoleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/tenants": {"get": {"tags": ["Tenants"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchQuery", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/tenants/{tenantId}": {"post": {"tags": ["Tenants"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateTenantRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateTenantRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateTenantRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/tenants/{tenantId}/status": {"patch": {"tags": ["Tenants"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateTenantStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateTenantStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateTenantStatusRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/tenants/{tenantId}/users": {"get": {"tags": ["Tenants"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SearchQuery", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Tenants"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.CreateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.CreateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.CreateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/tenants/{tenantId}/users/{userId}": {"delete": {"tags": ["Tenants"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/tenants/{tenantId}/users/{userId}/delegate_access": {"post": {"tags": ["Tenants"], "parameters": [{"name": "tenantId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users": {"get": {"tags": ["Users"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.CreateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.CreateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.CreateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/{userId}/status": {"patch": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateUserStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateUserStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pukpara.User.Management.Application.DTOs.Requests.UpdateUserStatusRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/{userId}/roles/{roleId}": {"post": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users/{userId}/roles": {"delete": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/users/{userId}/permissions": {"get": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Pukpara.User.Management.Domain.Models.AppPermission"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Pukpara.User.Management.Domain.Models.AppPermission"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Pukpara.User.Management.Domain.Models.AppPermission"}}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Users"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"Pukpara.Shared.Exceptions.PukparaException": {"type": "object", "properties": {"targetSite": {"$ref": "#/components/schemas/System.Reflection.MethodBase"}, "data": {"type": "object", "additionalProperties": {}, "nullable": true, "readOnly": true}, "innerException": {"$ref": "#/components/schemas/System.Exception"}, "helpLink": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "hResult": {"type": "integer", "format": "int32"}, "stackTrace": {"type": "string", "nullable": true, "readOnly": true}, "statusCode": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "details": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.Shared.Models.DTOs.PagedSearchFilter": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "searchQuery": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.Shared.Models.DTOs.Response": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "statusCode": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.AddPermmissionsToRoleRequest": {"type": "object", "properties": {"permissionIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.ChangePasswordRequest": {"type": "object", "properties": {"oldPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.CreateRoleRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.CreateUserRequest": {"type": "object", "properties": {"firstname": {"type": "string", "nullable": true}, "lastname": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "roleId": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "districtId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.ResetPasswordRequest": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.UpdatePermissionDescriptionRequest": {"type": "object", "properties": {"description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.UpdateRoleRequest": {"type": "object", "properties": {"description": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.UpdateTenantRequest": {"type": "object", "properties": {"adminUserId": {"type": "string", "nullable": true}, "workspaceName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.UpdateTenantStatusRequest": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "disablingReasons": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.UpdateUserStatusRequest": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.UserLoginRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Requests.UserRegistrationRequest": {"type": "object", "properties": {"firstname": {"type": "string", "nullable": true}, "lastname": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "districtId": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Application.DTOs.Responses.LoginResponse": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "statusCode": {"type": "integer", "format": "int32"}, "token": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "modules": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}, "tenantId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Pukpara.User.Management.Domain.Models.AppPermission": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "System.Exception": {"type": "object", "properties": {"targetSite": {"$ref": "#/components/schemas/System.Reflection.MethodBase"}, "message": {"type": "string", "nullable": true, "readOnly": true}, "data": {"type": "object", "additionalProperties": {}, "nullable": true, "readOnly": true}, "innerException": {"$ref": "#/components/schemas/System.Exception"}, "helpLink": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "hResult": {"type": "integer", "format": "int32"}, "stackTrace": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "System.IntPtr": {"type": "object", "additionalProperties": false}, "System.ModuleHandle": {"type": "object", "properties": {"mdStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "System.Reflection.Assembly": {"type": "object", "properties": {"definedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.TypeInfo"}, "nullable": true, "readOnly": true}, "exportedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Type"}, "nullable": true, "readOnly": true}, "codeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "entryPoint": {"$ref": "#/components/schemas/System.Reflection.MethodInfo"}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "imageRuntimeVersion": {"type": "string", "nullable": true, "readOnly": true}, "isDynamic": {"type": "boolean", "readOnly": true}, "location": {"type": "string", "nullable": true, "readOnly": true}, "reflectionOnly": {"type": "boolean", "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "isFullyTrusted": {"type": "boolean", "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "escapedCodeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "manifestModule": {"$ref": "#/components/schemas/System.Reflection.Module"}, "modules": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.Module"}, "nullable": true, "readOnly": true}, "globalAssemblyCache": {"type": "boolean", "readOnly": true, "deprecated": true}, "hostContext": {"type": "integer", "format": "int64", "readOnly": true}, "securityRuleSet": {"$ref": "#/components/schemas/System.Security.SecurityRuleSet"}}, "additionalProperties": false}, "System.Reflection.CallingConventions": {"enum": [1, 2, 3, 32, 64], "type": "integer", "format": "int32"}, "System.Reflection.ConstructorInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/System.Type"}, "reflectedType": {"$ref": "#/components/schemas/System.Type"}, "module": {"$ref": "#/components/schemas/System.Reflection.Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/System.Reflection.MethodAttributes"}, "methodImplementationFlags": {"$ref": "#/components/schemas/System.Reflection.MethodImplAttributes"}, "callingConvention": {"$ref": "#/components/schemas/System.Reflection.CallingConventions"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/System.RuntimeMethodHandle"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/System.Reflection.MemberTypes"}}, "additionalProperties": false}, "System.Reflection.CustomAttributeData": {"type": "object", "properties": {"attributeType": {"$ref": "#/components/schemas/System.Type"}, "constructor": {"$ref": "#/components/schemas/System.Reflection.ConstructorInfo"}, "constructorArguments": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeTypedArgument"}, "nullable": true, "readOnly": true}, "namedArguments": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeNamedArgument"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "System.Reflection.CustomAttributeNamedArgument": {"type": "object", "properties": {"memberInfo": {"$ref": "#/components/schemas/System.Reflection.MemberInfo"}, "typedValue": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeTypedArgument"}, "memberName": {"type": "string", "nullable": true, "readOnly": true}, "isField": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "System.Reflection.CustomAttributeTypedArgument": {"type": "object", "properties": {"argumentType": {"$ref": "#/components/schemas/System.Type"}, "value": {"nullable": true}}, "additionalProperties": false}, "System.Reflection.EventAttributes": {"enum": [0, 512, 1024], "type": "integer", "format": "int32"}, "System.Reflection.EventInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/System.Type"}, "reflectedType": {"$ref": "#/components/schemas/System.Type"}, "module": {"$ref": "#/components/schemas/System.Reflection.Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/System.Reflection.MemberTypes"}, "attributes": {"$ref": "#/components/schemas/System.Reflection.EventAttributes"}, "isSpecialName": {"type": "boolean", "readOnly": true}, "addMethod": {"$ref": "#/components/schemas/System.Reflection.MethodInfo"}, "removeMethod": {"$ref": "#/components/schemas/System.Reflection.MethodInfo"}, "raiseMethod": {"$ref": "#/components/schemas/System.Reflection.MethodInfo"}, "isMulticast": {"type": "boolean", "readOnly": true}, "eventHandlerType": {"$ref": "#/components/schemas/System.Type"}}, "additionalProperties": false}, "System.Reflection.FieldAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 16, 32, 64, 128, 256, 512, 1024, 4096, 8192, 32768, 38144], "type": "integer", "format": "int32"}, "System.Reflection.FieldInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/System.Type"}, "reflectedType": {"$ref": "#/components/schemas/System.Type"}, "module": {"$ref": "#/components/schemas/System.Reflection.Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/System.Reflection.MemberTypes"}, "attributes": {"$ref": "#/components/schemas/System.Reflection.FieldAttributes"}, "fieldType": {"$ref": "#/components/schemas/System.Type"}, "isInitOnly": {"type": "boolean", "readOnly": true}, "isLiteral": {"type": "boolean", "readOnly": true}, "isNotSerialized": {"type": "boolean", "readOnly": true, "deprecated": true}, "isPinvokeImpl": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "fieldHandle": {"$ref": "#/components/schemas/System.RuntimeFieldHandle"}}, "additionalProperties": false}, "System.Reflection.GenericParameterAttributes": {"enum": [0, 1, 2, 3, 4, 8, 16, 28], "type": "integer", "format": "int32"}, "System.Reflection.ICustomAttributeProvider": {"type": "object", "additionalProperties": false}, "System.Reflection.MemberInfo": {"type": "object", "properties": {"memberType": {"$ref": "#/components/schemas/System.Reflection.MemberTypes"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/System.Type"}, "reflectedType": {"$ref": "#/components/schemas/System.Type"}, "module": {"$ref": "#/components/schemas/System.Reflection.Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "System.Reflection.MemberTypes": {"enum": [1, 2, 4, 8, 16, 32, 64, 128, 191], "type": "integer", "format": "int32"}, "System.Reflection.MethodAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 53248], "type": "integer", "format": "int32"}, "System.Reflection.MethodBase": {"type": "object", "properties": {"memberType": {"$ref": "#/components/schemas/System.Reflection.MemberTypes"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/System.Type"}, "reflectedType": {"$ref": "#/components/schemas/System.Type"}, "module": {"$ref": "#/components/schemas/System.Reflection.Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/System.Reflection.MethodAttributes"}, "methodImplementationFlags": {"$ref": "#/components/schemas/System.Reflection.MethodImplAttributes"}, "callingConvention": {"$ref": "#/components/schemas/System.Reflection.CallingConventions"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/System.RuntimeMethodHandle"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "System.Reflection.MethodImplAttributes": {"enum": [0, 1, 2, 3, 4, 8, 16, 32, 64, 128, 256, 512, 4096, 65535], "type": "integer", "format": "int32"}, "System.Reflection.MethodInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/System.Type"}, "reflectedType": {"$ref": "#/components/schemas/System.Type"}, "module": {"$ref": "#/components/schemas/System.Reflection.Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "attributes": {"$ref": "#/components/schemas/System.Reflection.MethodAttributes"}, "methodImplementationFlags": {"$ref": "#/components/schemas/System.Reflection.MethodImplAttributes"}, "callingConvention": {"$ref": "#/components/schemas/System.Reflection.CallingConventions"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isConstructor": {"type": "boolean", "readOnly": true}, "isFinal": {"type": "boolean", "readOnly": true}, "isHideBySig": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isStatic": {"type": "boolean", "readOnly": true}, "isVirtual": {"type": "boolean", "readOnly": true}, "isAssembly": {"type": "boolean", "readOnly": true}, "isFamily": {"type": "boolean", "readOnly": true}, "isFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "isFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "isPrivate": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethod": {"type": "boolean", "readOnly": true}, "isGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "methodHandle": {"$ref": "#/components/schemas/System.RuntimeMethodHandle"}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/System.Reflection.MemberTypes"}, "returnParameter": {"$ref": "#/components/schemas/System.Reflection.ParameterInfo"}, "returnType": {"$ref": "#/components/schemas/System.Type"}, "returnTypeCustomAttributes": {"$ref": "#/components/schemas/System.Reflection.ICustomAttributeProvider"}}, "additionalProperties": false}, "System.Reflection.Module": {"type": "object", "properties": {"assembly": {"$ref": "#/components/schemas/System.Reflection.Assembly"}, "fullyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "name": {"type": "string", "nullable": true, "readOnly": true}, "mdStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}, "moduleVersionId": {"type": "string", "format": "uuid", "readOnly": true}, "scopeName": {"type": "string", "nullable": true, "readOnly": true}, "moduleHandle": {"$ref": "#/components/schemas/System.ModuleHandle"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "System.Reflection.ParameterAttributes": {"enum": [0, 1, 2, 4, 8, 16, 4096, 8192, 16384, 32768, 61440], "type": "integer", "format": "int32"}, "System.Reflection.ParameterInfo": {"type": "object", "properties": {"attributes": {"$ref": "#/components/schemas/System.Reflection.ParameterAttributes"}, "member": {"$ref": "#/components/schemas/System.Reflection.MemberInfo"}, "name": {"type": "string", "nullable": true, "readOnly": true}, "parameterType": {"$ref": "#/components/schemas/System.Type"}, "position": {"type": "integer", "format": "int32", "readOnly": true}, "isIn": {"type": "boolean", "readOnly": true}, "isLcid": {"type": "boolean", "readOnly": true}, "isOptional": {"type": "boolean", "readOnly": true}, "isOut": {"type": "boolean", "readOnly": true}, "isRetval": {"type": "boolean", "readOnly": true}, "defaultValue": {"nullable": true, "readOnly": true}, "rawDefaultValue": {"nullable": true, "readOnly": true}, "hasDefaultValue": {"type": "boolean", "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "System.Reflection.PropertyAttributes": {"enum": [0, 512, 1024, 4096, 8192, 16384, 32768, 62464], "type": "integer", "format": "int32"}, "System.Reflection.PropertyInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/System.Type"}, "reflectedType": {"$ref": "#/components/schemas/System.Type"}, "module": {"$ref": "#/components/schemas/System.Reflection.Module"}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/System.Reflection.MemberTypes"}, "propertyType": {"$ref": "#/components/schemas/System.Type"}, "attributes": {"$ref": "#/components/schemas/System.Reflection.PropertyAttributes"}, "isSpecialName": {"type": "boolean", "readOnly": true}, "canRead": {"type": "boolean", "readOnly": true}, "canWrite": {"type": "boolean", "readOnly": true}, "getMethod": {"$ref": "#/components/schemas/System.Reflection.MethodInfo"}, "setMethod": {"$ref": "#/components/schemas/System.Reflection.MethodInfo"}}, "additionalProperties": false}, "System.Reflection.TypeAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 24, 32, 128, 256, 1024, 2048, 4096, 8192, 16384, 65536, 131072, 196608, 262144, 264192, 1048576, 12582912], "type": "integer", "format": "int32"}, "System.Reflection.TypeInfo": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "isInterface": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/System.Reflection.MemberTypes"}, "namespace": {"type": "string", "nullable": true, "readOnly": true}, "assemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "assembly": {"$ref": "#/components/schemas/System.Reflection.Assembly"}, "module": {"$ref": "#/components/schemas/System.Reflection.Module"}, "isNested": {"type": "boolean", "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/System.Type"}, "declaringMethod": {"$ref": "#/components/schemas/System.Reflection.MethodBase"}, "reflectedType": {"$ref": "#/components/schemas/System.Type"}, "underlyingSystemType": {"$ref": "#/components/schemas/System.Type"}, "isTypeDefinition": {"type": "boolean", "readOnly": true}, "isArray": {"type": "boolean", "readOnly": true}, "isByRef": {"type": "boolean", "readOnly": true}, "isPointer": {"type": "boolean", "readOnly": true}, "isConstructedGenericType": {"type": "boolean", "readOnly": true}, "isGenericParameter": {"type": "boolean", "readOnly": true}, "isGenericTypeParameter": {"type": "boolean", "readOnly": true}, "isGenericMethodParameter": {"type": "boolean", "readOnly": true}, "isGenericType": {"type": "boolean", "readOnly": true}, "isGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "isSZArray": {"type": "boolean", "readOnly": true}, "isVariableBoundArray": {"type": "boolean", "readOnly": true}, "isByRefLike": {"type": "boolean", "readOnly": true}, "isFunctionPointer": {"type": "boolean", "readOnly": true}, "isUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "hasElementType": {"type": "boolean", "readOnly": true}, "genericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/System.Type"}, "nullable": true, "readOnly": true}, "genericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "genericParameterAttributes": {"$ref": "#/components/schemas/System.Reflection.GenericParameterAttributes"}, "attributes": {"$ref": "#/components/schemas/System.Reflection.TypeAttributes"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isImport": {"type": "boolean", "readOnly": true}, "isSealed": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isClass": {"type": "boolean", "readOnly": true}, "isNestedAssembly": {"type": "boolean", "readOnly": true}, "isNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "isNestedFamily": {"type": "boolean", "readOnly": true}, "isNestedFamORAssem": {"type": "boolean", "readOnly": true}, "isNestedPrivate": {"type": "boolean", "readOnly": true}, "isNestedPublic": {"type": "boolean", "readOnly": true}, "isNotPublic": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isAutoLayout": {"type": "boolean", "readOnly": true}, "isExplicitLayout": {"type": "boolean", "readOnly": true}, "isLayoutSequential": {"type": "boolean", "readOnly": true}, "isAnsiClass": {"type": "boolean", "readOnly": true}, "isAutoClass": {"type": "boolean", "readOnly": true}, "isUnicodeClass": {"type": "boolean", "readOnly": true}, "isCOMObject": {"type": "boolean", "readOnly": true}, "isContextful": {"type": "boolean", "readOnly": true}, "isEnum": {"type": "boolean", "readOnly": true}, "isMarshalByRef": {"type": "boolean", "readOnly": true}, "isPrimitive": {"type": "boolean", "readOnly": true}, "isValueType": {"type": "boolean", "readOnly": true}, "isSignatureType": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "structLayoutAttribute": {"$ref": "#/components/schemas/System.Runtime.InteropServices.StructLayoutAttribute"}, "typeInitializer": {"$ref": "#/components/schemas/System.Reflection.ConstructorInfo"}, "typeHandle": {"$ref": "#/components/schemas/System.RuntimeTypeHandle"}, "guid": {"type": "string", "format": "uuid", "readOnly": true}, "baseType": {"$ref": "#/components/schemas/System.Type"}, "isSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "isVisible": {"type": "boolean", "readOnly": true}, "genericTypeParameters": {"type": "array", "items": {"$ref": "#/components/schemas/System.Type"}, "nullable": true, "readOnly": true}, "declaredConstructors": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.ConstructorInfo"}, "nullable": true, "readOnly": true}, "declaredEvents": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.EventInfo"}, "nullable": true, "readOnly": true}, "declaredFields": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.FieldInfo"}, "nullable": true, "readOnly": true}, "declaredMembers": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.MemberInfo"}, "nullable": true, "readOnly": true}, "declaredMethods": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.MethodInfo"}, "nullable": true, "readOnly": true}, "declaredNestedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.TypeInfo"}, "nullable": true, "readOnly": true}, "declaredProperties": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.PropertyInfo"}, "nullable": true, "readOnly": true}, "implementedInterfaces": {"type": "array", "items": {"$ref": "#/components/schemas/System.Type"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "System.Runtime.InteropServices.LayoutKind": {"enum": [0, 2, 3], "type": "integer", "format": "int32"}, "System.Runtime.InteropServices.StructLayoutAttribute": {"type": "object", "properties": {"typeId": {"nullable": true, "readOnly": true}, "value": {"$ref": "#/components/schemas/System.Runtime.InteropServices.LayoutKind"}}, "additionalProperties": false}, "System.RuntimeFieldHandle": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/System.IntPtr"}}, "additionalProperties": false}, "System.RuntimeMethodHandle": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/System.IntPtr"}}, "additionalProperties": false}, "System.RuntimeTypeHandle": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/System.IntPtr"}}, "additionalProperties": false}, "System.Security.SecurityRuleSet": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "System.Type": {"type": "object", "properties": {"name": {"type": "string", "nullable": true, "readOnly": true}, "customAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/System.Reflection.CustomAttributeData"}, "nullable": true, "readOnly": true}, "isCollectible": {"type": "boolean", "readOnly": true}, "metadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "isInterface": {"type": "boolean", "readOnly": true}, "memberType": {"$ref": "#/components/schemas/System.Reflection.MemberTypes"}, "namespace": {"type": "string", "nullable": true, "readOnly": true}, "assemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}, "assembly": {"$ref": "#/components/schemas/System.Reflection.Assembly"}, "module": {"$ref": "#/components/schemas/System.Reflection.Module"}, "isNested": {"type": "boolean", "readOnly": true}, "declaringType": {"$ref": "#/components/schemas/System.Type"}, "declaringMethod": {"$ref": "#/components/schemas/System.Reflection.MethodBase"}, "reflectedType": {"$ref": "#/components/schemas/System.Type"}, "underlyingSystemType": {"$ref": "#/components/schemas/System.Type"}, "isTypeDefinition": {"type": "boolean", "readOnly": true}, "isArray": {"type": "boolean", "readOnly": true}, "isByRef": {"type": "boolean", "readOnly": true}, "isPointer": {"type": "boolean", "readOnly": true}, "isConstructedGenericType": {"type": "boolean", "readOnly": true}, "isGenericParameter": {"type": "boolean", "readOnly": true}, "isGenericTypeParameter": {"type": "boolean", "readOnly": true}, "isGenericMethodParameter": {"type": "boolean", "readOnly": true}, "isGenericType": {"type": "boolean", "readOnly": true}, "isGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "isSZArray": {"type": "boolean", "readOnly": true}, "isVariableBoundArray": {"type": "boolean", "readOnly": true}, "isByRefLike": {"type": "boolean", "readOnly": true}, "isFunctionPointer": {"type": "boolean", "readOnly": true}, "isUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "hasElementType": {"type": "boolean", "readOnly": true}, "genericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/System.Type"}, "nullable": true, "readOnly": true}, "genericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "genericParameterAttributes": {"$ref": "#/components/schemas/System.Reflection.GenericParameterAttributes"}, "attributes": {"$ref": "#/components/schemas/System.Reflection.TypeAttributes"}, "isAbstract": {"type": "boolean", "readOnly": true}, "isImport": {"type": "boolean", "readOnly": true}, "isSealed": {"type": "boolean", "readOnly": true}, "isSpecialName": {"type": "boolean", "readOnly": true}, "isClass": {"type": "boolean", "readOnly": true}, "isNestedAssembly": {"type": "boolean", "readOnly": true}, "isNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "isNestedFamily": {"type": "boolean", "readOnly": true}, "isNestedFamORAssem": {"type": "boolean", "readOnly": true}, "isNestedPrivate": {"type": "boolean", "readOnly": true}, "isNestedPublic": {"type": "boolean", "readOnly": true}, "isNotPublic": {"type": "boolean", "readOnly": true}, "isPublic": {"type": "boolean", "readOnly": true}, "isAutoLayout": {"type": "boolean", "readOnly": true}, "isExplicitLayout": {"type": "boolean", "readOnly": true}, "isLayoutSequential": {"type": "boolean", "readOnly": true}, "isAnsiClass": {"type": "boolean", "readOnly": true}, "isAutoClass": {"type": "boolean", "readOnly": true}, "isUnicodeClass": {"type": "boolean", "readOnly": true}, "isCOMObject": {"type": "boolean", "readOnly": true}, "isContextful": {"type": "boolean", "readOnly": true}, "isEnum": {"type": "boolean", "readOnly": true}, "isMarshalByRef": {"type": "boolean", "readOnly": true}, "isPrimitive": {"type": "boolean", "readOnly": true}, "isValueType": {"type": "boolean", "readOnly": true}, "isSignatureType": {"type": "boolean", "readOnly": true}, "isSecurityCritical": {"type": "boolean", "readOnly": true}, "isSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "isSecurityTransparent": {"type": "boolean", "readOnly": true}, "structLayoutAttribute": {"$ref": "#/components/schemas/System.Runtime.InteropServices.StructLayoutAttribute"}, "typeInitializer": {"$ref": "#/components/schemas/System.Reflection.ConstructorInfo"}, "typeHandle": {"$ref": "#/components/schemas/System.RuntimeTypeHandle"}, "guid": {"type": "string", "format": "uuid", "readOnly": true}, "baseType": {"$ref": "#/components/schemas/System.Type"}, "isSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "containsGenericParameters": {"type": "boolean", "readOnly": true}, "isVisible": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}}}}