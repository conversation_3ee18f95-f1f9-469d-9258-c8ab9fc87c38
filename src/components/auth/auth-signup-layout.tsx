import { ArrowLef<PERSON> } from "lucide-react";
import Image from "next/image";
import { Button } from "@/components/ui/button";

interface AuthSignupLayoutProps {
	children: React.ReactNode;
	currentStep?: number;
	onBackClick?: () => void;
}

export function AuthSignupLayout({
	children,
	currentStep,
	onBackClick,
}: AuthSignupLayoutProps) {
	return (
		<div className="min-h-screen flex flex-col lg:flex-row">
			{/* Left side - Form Content */}
			<div className="flex-1 flex flex-col justify-center p-4 sm:p-8 bg-background">
				<div className="w-full max-w-md mx-auto">
					{/* Logo with back button overlay */}
					<div className="flex justify-center lg:justify-start relative">
						{/* Back button - positioned absolutely over logo area */}
						{currentStep === 2 && onBackClick && (
							<Button
								variant="ghost"
								size="sm"
								className="absolute left-0 top-0 z-10 w-fit p-2 h-auto text-muted-foreground hover:text-foreground lg:left-0 lg:top-0"
								onClick={onBackClick}
							>
								<ArrowLeft className="w-4 h-4" />
								<span className="sr-only">Back</span>
							</Button>
						)}

						<Image
							src="/pukpara-logo.png"
							alt="Pukpara Logo"
							width={194}
							height={55}
						/>
					</div>

					{/* Form Content */}
					{children}
				</div>
			</div>

			{/* Right side - Image */}
			<div className="hidden lg:flex lg:flex-1 relative">
				<Image
					src="/auth-image2.jpg"
					alt="Authentication background"
					fill
					className="object-cover"
					priority
				/>
			</div>
		</div>
	);
}
