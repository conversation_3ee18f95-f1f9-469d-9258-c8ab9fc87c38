// Base API response structure
export interface ApiResponse<T = any> {
	data?: T;
	message: string;
	statusCode: number;
}

// Error response structure
export interface ApiError {
	message: string;
	statusCode: number;
	details?: string;
}

// Authentication types
export interface LoginRequest {
	username: string;
	password: string;
}

export interface LoginResponse {
	token: string;
	userName: string;
	userId: string;
	modules: string[];
	tenantId: string;
	message: string;
	statusCode: number;
}

export interface RegisterRequest {
	firstname: string;
	lastname: string;
	email: string;
	phoneNumber: string;
	districtId: string;
	address: string;
	password: string;
	confirmPassword: string;
}

export interface RegisterResponse {
	message: string;
	statusCode: number;
}

export interface ForgotPasswordRequest {
	email: string;
}

export interface ForgotPasswordResponse {
	message: string;
	statusCode: number;
}

export interface ResetPasswordRequest {
	token: string;
	newPassword: string;
	confirmPassword: string;
}

export interface ResetPasswordResponse {
	message: string;
	statusCode: number;
}

// Districts types
export interface District {
	id: string;
	name: string;
	region: string | null;
}

export interface DistrictsRequest {
	pageNumber: number;
	pageSize: number;
}

export interface DistrictsResponse {
	data: District[];
	message: string | null;
	statusCode: number;
}

// Generic pagination types
export interface PaginationParams {
	pageNumber?: number;
	pageSize?: number;
}

export interface PaginatedResponse<T> {
	data: T[];
	totalCount: number;
	pageNumber: number;
	pageSize: number;
	totalPages: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
}
