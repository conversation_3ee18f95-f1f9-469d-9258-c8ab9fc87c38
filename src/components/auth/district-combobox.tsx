"use client";

import { CheckIcon, ChevronsUpDownIcon } from "lucide-react";
import * as React from "react";
import type { Control, FieldPath, FieldValues } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { districts, type District } from "@/lib/api";

interface DistrictComboboxProps<
	TFieldValues extends FieldValues = FieldValues,
	TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
	control: Control<TFieldValues>;
	name: TName;
	label: string;
	placeholder: string;
}

export function DistrictCombobox<
	TFieldValues extends FieldValues = FieldValues,
	TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
	control,
	name,
	label,
	placeholder,
}: DistrictComboboxProps<TFieldValues, TName>) {
	const [open, setOpen] = React.useState(false);
	const [districtsData, setDistrictsData] = React.useState<District[]>([]);
	const [loading, setLoading] = React.useState(true);
	const triggerRef = React.useRef<HTMLButtonElement>(null);

	// Fetch districts on component mount
	React.useEffect(() => {
		const fetchDistricts = async () => {
			try {
				const response = await districts.getAll();
				setDistrictsData(response.data);
			} catch (error) {
				console.error("Failed to fetch districts:", error);
				setDistrictsData([]);
			} finally {
				setLoading(false);
			}
		};

		fetchDistricts();
	}, []);

	return (
		<FormField
			control={control}
			name={name}
			render={({ field }) => (
				<FormItem>
					<FormLabel className="text-sm font-medium text-foreground">
						{label}
					</FormLabel>
					<Popover open={open} onOpenChange={setOpen}>
						<PopoverTrigger asChild>
							<FormControl>
								<Button
									ref={triggerRef}
									variant="outline"
									aria-expanded={open}
									className="w-full justify-between"
									disabled={loading}
								>
									{field.value
										? districtsData.find(
												(district) => district.id === field.value,
											)?.name
										: loading
											? "Loading districts..."
											: placeholder}
									<ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
								</Button>
							</FormControl>
						</PopoverTrigger>
						<PopoverContent
							className="p-0"
							style={{ width: triggerRef.current?.offsetWidth }}
						>
							<Command>
								<CommandInput
									placeholder={`Search ${label.toLowerCase()}...`}
								/>
								<CommandList>
									<CommandEmpty>
										{loading ? "Loading districts..." : "No district found."}
									</CommandEmpty>
									<CommandGroup>
										{districtsData.map((district) => (
											<CommandItem
												key={district.id}
												value={district.id}
												onSelect={(currentValue) => {
													field.onChange(
														currentValue === field.value ? "" : currentValue,
													);
													setOpen(false);
												}}
											>
												<CheckIcon
													className={cn(
														"mr-2 h-4 w-4",
														field.value === district.id
															? "opacity-100"
															: "opacity-0",
													)}
												/>
												{district.name}
											</CommandItem>
										))}
									</CommandGroup>
								</CommandList>
							</Command>
						</PopoverContent>
					</Popover>
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}
