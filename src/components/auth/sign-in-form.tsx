"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod/v3";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { auth, type ApiError } from "@/lib/api";
import { useAuth } from "@/contexts/auth-context";

const formSchema = z.object({
	username: z
		.string()
		.min(1, {
			message: "Email is required.",
		})
		.email("Please enter a valid email address"),

	password: z
		.string()
		.min(1, {
			message: "Password is required.",
		})
		.min(4, {
			message: "Password must be at least 4 characters.",
		}),
	keepSignedIn: z.boolean(),
});

export function SignInForm() {
	const router = useRouter();
	const { login } = useAuth();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// 1. Define your form.
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			username: "",
			password: "",
			keepSignedIn: false,
		},
	});

	// 2. Define a submit handler.
	async function onSubmit(values: z.infer<typeof formSchema>) {
		try {
			setIsLoading(true);
			setError(null);

			const response = await auth.login({
				username: values.username,
				password: values.password,
			});

			// Store authentication data using context
			login(response.token, {
				token: response.token,
				userName: response.userName,
				userId: response.userId,
				modules: response.modules,
				tenantId: response.tenantId,
			});

			// Redirect to dashboard
			router.push("/");
		} catch (error) {
			// Handle different types of errors
			if (error instanceof Error) {
				setError(error.message);
			} else if (error && typeof error === "object" && "message" in error) {
				setError((error as ApiError).message);
			} else if (typeof error === "string") {
				setError(error);
			} else {
				setError("An unexpected error occurred. Please try again.");
			}
		} finally {
			setIsLoading(false);
		}
	}

	return (
		<Card className="border-0 shadow-none mt-8">
			<CardHeader className="space-y-2 text-left lg:text-left p-0">
				<CardTitle className="text-xl font-semibold text-foreground leading-7">
					Welcome back to Pukpara
				</CardTitle>
				<CardDescription className="text-sm text-muted-foreground leading-5">
					Sign in to continue managing your farm operations
				</CardDescription>
			</CardHeader>
			<CardContent className="p-0 mt-6">
				<Form {...form}>
					<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
						{/* Display error message */}
						{error && (
							<div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
								{error}
							</div>
						)}

						{/* Email Field */}
						<FormField
							control={form.control}
							name="username"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Email
									</FormLabel>
									<FormControl>
										<Input
											type="email"
											placeholder="<EMAIL>"
											disabled={isLoading}
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Password Field */}
						<FormField
							control={form.control}
							name="password"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium text-foreground">
										Password
									</FormLabel>
									<FormControl>
										<Input
											type="password"
											placeholder="Enter your password"
											disabled={isLoading}
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Keep me signed in & Forgot password */}
						<div className="flex items-center justify-between">
							<FormField
								control={form.control}
								name="keepSignedIn"
								render={({ field }) => (
									<FormItem className="flex flex-row items-start space-y-0">
										<FormControl>
											<Checkbox
												checked={field.value}
												onCheckedChange={field.onChange}
												disabled={isLoading}
											/>
										</FormControl>
										<div className="leading-none">
											<FormLabel className="text-sm font-normal text-foreground">
												Keep me signed in
											</FormLabel>
										</div>
									</FormItem>
								)}
							/>
							<Link
								href="/forgot-password"
								className="text-sm text-primary hover:underline"
							>
								Forgot password?
							</Link>
						</div>

						{/* Sign In Button */}
						<Button
							type="submit"
							className="w-full mt-6"
							size="lg"
							disabled={isLoading}
						>
							{isLoading ? "Signing in..." : "Sign In"}
						</Button>
					</form>
				</Form>

				{/* Sign Up Link */}
				<div className="mt-8 text-center">
					<span className="text-sm text-muted-foreground">
						New to Pukpara?{" "}
						<Link
							href="/sign-up"
							className="text-primary hover:underline font-medium"
						>
							Create an account
						</Link>
					</span>
				</div>
			</CardContent>
		</Card>
	);
}
