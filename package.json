{"name": "pukpara-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome check .", "lint:fix": "biome check --write .", "format": "biome format --write .", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@opennextjs/cloudflare": "^1.5.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-data-list": "^1.4.1", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-circle-flags": "^0.0.23", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-phone-number-input": "^3.4.12", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "wrangler": "^4.24.3"}}